#!/usr/bin/env python3
"""
Test script to verify the order details fix works.
"""
import json
import sys
import os

# Add the parent directory to the path so we can import ShopeeAPI modules
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from ShopeeAPI.client import <PERSON><PERSON><PERSON><PERSON>


def test_order_details_fix():
    """
    Test the fixed order details functionality.
    """
    print("=== Testing Order Details Fix ===")
    
    # Test order_sn provided by the user
    order_sn = "250604MPB2VBWR"
    
    try:
        # Initialize the API client
        api = ShopeeAPI()
        
        print(f"\nTesting order details for: {order_sn}")
        
        # Test the fixed method
        details_data, status_code = api.get_order_details(order_sn)
        
        print(f"Status Code: {status_code}")
        
        if status_code == 200:
            print("✅ SUCCESS: Order details retrieved!")
            
            # Check what type of data we got
            if 'data' in details_data:
                data = details_data['data']
                if 'source' in data and data['source'] == 'search_result':
                    print("📋 Returned basic order information from search (detailed endpoint failed)")
                    print(f"Order SN: {data.get('order_sn', 'N/A')}")
                    print(f"Order ID: {data.get('order_id', 'N/A')}")
                else:
                    print("📋 Returned detailed order information from specific endpoint")
                
                # Print a summary of the data
                print(f"\nData keys: {list(data.keys())}")
                
                # If it's basic info, show some details
                if 'basic_info' in data:
                    basic_info = data['basic_info']
                    print(f"Basic info keys: {list(basic_info.keys())}")
                    
                    # Try to extract some useful information
                    if 'package_level_order_card' in basic_info:
                        card = basic_info['package_level_order_card']
                        if 'card_header' in card:
                            header = card['card_header']
                            print(f"Order SN from header: {header.get('order_sn', 'N/A')}")
                            print(f"Create time: {header.get('create_time', 'N/A')}")
                    elif 'order_card' in basic_info:
                        card = basic_info['order_card']
                        if 'card_header' in card:
                            header = card['card_header']
                            print(f"Order SN from header: {header.get('order_sn', 'N/A')}")
                            print(f"Create time: {header.get('create_time', 'N/A')}")
            else:
                print("⚠️  Unexpected response format")
                print(f"Response keys: {list(details_data.keys())}")
        else:
            print(f"❌ FAILED: Status {status_code}")
            print(f"Error: {details_data}")
        
        return status_code == 200
        
    except Exception as e:
        print(f"❌ ERROR: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def test_api_endpoint():
    """
    Test if the API endpoint is accessible.
    """
    print("\n=== Testing API Endpoint Accessibility ===")
    
    try:
        # Initialize the API client
        api = ShopeeAPI()
        
        # Test a simple API call to check connectivity
        print("Testing API connectivity...")
        
        # Try to get to_ship orders (this should work if credentials are valid)
        try:
            orders = api.get_to_ship_orders()
            print("✅ API is accessible and credentials are valid")
            return True
        except Exception as e:
            error_msg = str(e).lower()
            if 'authentication' in error_msg or '403' in error_msg or '401' in error_msg:
                print("❌ Authentication failed - credentials may be expired")
                print("Please update your authorization_code and cookie in config.json")
            else:
                print(f"❌ API error: {str(e)}")
            return False
            
    except Exception as e:
        print(f"❌ Failed to initialize API: {str(e)}")
        return False


if __name__ == "__main__":
    print("Starting Order Details Fix Test...")
    
    # First test API connectivity
    api_ok = test_api_endpoint()
    
    if api_ok:
        # If API is accessible, test the order details fix
        success = test_order_details_fix()
        
        if success:
            print("\n🎉 Order details fix is working!")
        else:
            print("\n❌ Order details fix needs more work")
    else:
        print("\n⚠️  Cannot test order details fix due to API connectivity issues")
        print("Please update your credentials and try again")
    
    print("\nTest completed.")
