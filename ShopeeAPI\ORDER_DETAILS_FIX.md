# Order Details Fix Documentation

## Problem Description

The `/orders/{order_sn}/details` endpoint was returning errors when trying to get detailed order information:

1. **404 page not found** - When using the endpoint directly
2. **Order not found: 202711189213112** - When using order_id in the specific details endpoint
3. The issue occurred with order_sn: `250604MPB2VBWR`

## Root Cause Analysis

The original implementation in `get_order_details_by_sn` method had the following flow:

1. Search for order using `order_sn` to get `order_id`
2. Call `order_details_specific` endpoint with `order_id`
3. Return the detailed order information

The problem was that the `order_details_specific` endpoint might:
- Expect `order_sn` instead of `order_id`
- Have a different parameter format requirement
- Be the wrong endpoint entirely

## Solution Implemented

### Multi-Approach Strategy

The fix implements a robust multi-approach strategy that tries different methods to get order details:

#### 1. Direct Order SN Approach
```json
{
    "order_sn": "250604MPB2VBWR",
    "shop_id": 345602862,
    "region_id": "MY"
}
```

#### 2. Order ID Approach (Original)
```json
{
    "order_id": 202711189213112,
    "shop_id": 345602862,
    "region_id": "MY"
}
```

#### 3. Combined Approach
```json
{
    "order_id": 202711189213112,
    "order_sn": "250604MPB2VBWR",
    "shop_id": 345602862,
    "region_id": "MY"
}
```

#### 4. Alternative Endpoint
If available, tries the same payloads on `order_details_alternative` endpoint:
- `https://seller.shopee.com.my/api/v3/order/get_order_detail`

#### 5. Fallback to Basic Information
If all detailed endpoints fail, returns basic order information from the search results:
```json
{
    "data": {
        "order_sn": "250604MPB2VBWR",
        "order_id": 202711189213112,
        "basic_info": { /* original order data from search */ },
        "source": "search_result",
        "note": "Detailed order information not available, returning basic information from search"
    }
}
```

## Files Modified

### 1. `services/orders.py`
- **Method**: `get_order_details_by_sn`
- **Changes**: Implemented multi-approach strategy with fallback
- **Lines**: 389-534

### 2. `core/config.py`
- **Addition**: Added `order_details_alternative` endpoint
- **Lines**: 78

## Testing

### Debug Scripts Created
1. **`debug_order_details.py`** - Comprehensive debugging script
2. **`test_order_details_fix.py`** - Test script to verify the fix
3. **`services/orders_clean.py`** - Clean version without debug prints

### Test Cases
- ✅ Direct order_sn approach
- ✅ Order ID approach
- ✅ Combined payload approach
- ✅ Alternative endpoint approach
- ✅ Fallback to basic information

## Benefits of the Fix

1. **Robustness**: Multiple fallback approaches ensure the endpoint works even if one method fails
2. **Backward Compatibility**: Original functionality is preserved
3. **Better Error Handling**: Provides meaningful responses even when detailed info isn't available
4. **Debugging**: Comprehensive logging for troubleshooting
5. **Future-Proof**: Easy to add more approaches if needed

## Usage

The API endpoint remains the same:
```
GET /orders/{order_sn}/details
```

Example:
```bash
curl -X GET "http://localhost:8000/orders/250604MPB2VBWR/details"
```

## Response Types

### Success with Detailed Information
```json
{
    "data": {
        // Detailed order information from Shopee API
    }
}
```

### Success with Basic Information (Fallback)
```json
{
    "data": {
        "order_sn": "250604MPB2VBWR",
        "order_id": 202711189213112,
        "basic_info": {
            // Basic order information from search
        },
        "source": "search_result",
        "note": "Detailed order information not available, returning basic information from search"
    }
}
```

### Error Response
```json
{
    "error": "Order not found: 250604MPB2VBWR"
}
```

## Configuration

### Adding Alternative Endpoints
To add more alternative endpoints, update `core/config.py`:

```python
self.urls = {
    # ... existing URLs ...
    "order_details_alternative": "https://seller.shopee.com.my/api/v3/order/get_order_detail",
    "order_details_alternative2": "https://seller.shopee.com.my/api/v3/order/another_endpoint",
}
```

Then update the `get_order_details_by_sn` method to try these endpoints.

## Monitoring

The fix includes comprehensive error handling and logging. Monitor for:
- Which approach succeeds most often
- Frequency of fallback to basic information
- Any new error patterns

## Future Improvements

1. **Caching**: Cache successful approach per order type
2. **Analytics**: Track which method works best for different order statuses
3. **Configuration**: Make the approach order configurable
4. **Performance**: Optimize by trying the most successful approach first

## Troubleshooting

If the endpoint still fails:
1. Check authentication credentials
2. Verify the order exists using search endpoint
3. Check Shopee API documentation for endpoint changes
4. Review logs for specific error messages
5. Test with different order types/statuses
